class RouterName {
  static const String route = 'screenroute';
  static const String splashScreen = 'splash$route';
  static const String playgroundScreen = 'playground$route';
  static const String onboardingScreen = 'onboarding$route';
  static const String profilesettingScreen = 'profilesetting$route';
  static const String languageScreen = 'language$route';
  static const String selectedLanguageScreen = 'selectedlanguage$route';
  static const String questionnaireScreen = 'questionnaire$route';
  static const String questionnaireFinishScreen = 'questionnairefinish$route';
  static const String certificateListScreen = 'certificatelist$route';
  static const String certificateScreen = 'certificate$route';
  static const String certificateDetailScreen = 'certificatedetail$route';
  static const String notificationSettingScreen = 'notificationsetting$route';

  static const String dashboardScreen = 'dashboard$route';

  ////////////////////////////////Authentication
  static const String signInScreen = 'signin$route';

  ////////////////////////////////Diagnostic Test
  static const String diagnosticTestOnboardScreen =
      'diagnostictestonboard$route';
  static const String diagnosticTestScreen = 'diagnostictest$route';
  static const String diagnosticTestResultScreen = 'diagnostictestresult$route';
  static const String diagnosticTestCongratulationScreen =
      'diagnostictestcongratulation$route';
  static const String diagnosticTestInstScreen = 'diagnostictestinst$route';

  /// TitleScreen
  static const String chapterTitle = 'chaptertitle$route';

  ////////////////////////////////Pronunciation Challenge
  static const String pronunciationChallenge = 'pronunciationchallenge$route';
  static const String pronunciationChallengeOnboarding =
      'pronunciationchallengeonboarding$route';
  static const String pronunciationChallengeInstruction =
      'pronunciationchallengeinstruction$route';
  static const String pronunciationChallengeContentResult =
      'pronunciationchallengecontentresult$route';
  static const String pronunciationChallengeResult =
      'pronunciationchallengeresult$route';

  ////////////////////////////////Conversation Video
  static const String conversationVideo = 'conversationvideo$route';
  static const String conversationVideoOnboarding =
      'conversationvideoonboarding$route';
  static const String conversationVideoInstruction =
      'conversationvideoinstruction$route';
  static const String conversationVideoResult = 'conversationvideoresult$route';

  ////////////////////////////////Listening Mastery
  static const String listeningMastery = 'listeningmastery$route';
  static const String listeningMasteryOnboarding =
      'listeningmasteryonboarding$route';
  static const String listeningMasteryInstruction =
      'listeningmasteryinstruction$route';
  static const String listeningMasteryContentResult =
      'listeningmasterycontentresult$route';
  static const String listeningMasteryResult = 'listeningmasteryresult$route';

  ////////////////////////////////Listening Mastery
  static const String speakingArena = 'speakingarena$route';
  static const String speakingArenaStage = 'speakingarenastage$route';
  static const String speakingArenaOnboarding = 'speakingarenaonboarding$route';
  static const String speakingArenaInstruction =
      'speakingarenainstruction$route';
  static const String speakingArenaContentResult =
      'speakingarenacontentresult$route';
  static const String speakingArenaResult = 'speakingarenaresult$route';

  static const String certificateNotification = 'certificatenotification$route';

  ////////////////////////////////Library
  static const String library = 'library$route';
  static const String libraryChapter = 'librarychapter$route';
  static const String libraryChapterContent = 'librarychaptercontent$route';

  ////////////////////////////////Game
  static const String topicGame = 'topicgame$route';
  static const String memoryFlash = 'memoryflash$route';

  /// Search
  static const String search = 'search$route';
  
  /// Bookmark
  static const String bookmarkScreen = 'bookmark$route';
}
