part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<BookmarkRoute>(
  path: '/bookmarks',
  name: RouterName.bookmarkScreen,
)
class BookmarkRoute extends GoRouteData with _$BookmarkRoute {
  const BookmarkRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      bookmark_screen.loadLibrary,
      () => bookmark_screen.BookmarkScreen(),
    );
  }
}